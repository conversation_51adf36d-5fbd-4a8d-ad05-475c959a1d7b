// 数据库初始化工具
import { DatabaseOperations } from './database'
import { runDatabaseMigrations } from './database-migration'

export async function initializeDatabase() {
  try {
    console.log('开始初始化数据库...')

    // 测试数据库连接
    const isConnected = await DatabaseOperations.testConnection()
    if (!isConnected) {
      throw new Error('数据库连接测试失败')
    }

    // 运行数据库迁移
    await runDatabaseMigrations()

    console.log('数据库初始化完成')
    return true
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return false
  }
}

// 在应用启动时自动初始化数据库
if (typeof window === 'undefined') {
  // 只在服务器端执行
  initializeDatabase().catch(console.error)
}
