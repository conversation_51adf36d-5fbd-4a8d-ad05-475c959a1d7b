// 数据库迁移工具
import { db } from './database'

export class DatabaseMigration {
  // 检查enabled字段是否存在
  static async checkEnabledFieldExists(): Promise<boolean> {
    try {
      const result = await db.get<any>('PRAGMA table_info(sources)')
      if (!result) return false
      
      // 获取所有列信息
      const columns = await db.all<any>('PRAGMA table_info(sources)')
      return columns.some(col => col.name === 'enabled')
    } catch (error) {
      console.error('Failed to check enabled field:', error)
      return false
    }
  }

  // 添加enabled字段到sources表
  static async addEnabledField(): Promise<void> {
    try {
      const fieldExists = await this.checkEnabledFieldExists()
      if (fieldExists) {
        console.log('Enabled field already exists, skipping migration')
        return
      }

      console.log('Adding enabled field to sources table...')
      
      // 添加enabled字段，默认值为0（false）
      await db.run('ALTER TABLE sources ADD COLUMN enabled BOOLEAN DEFAULT 0')
      
      console.log('Enabled field added successfully')
    } catch (error) {
      console.error('Failed to add enabled field:', error)
      throw error
    }
  }

  // 运行所有必要的迁移
  static async runMigrations(): Promise<void> {
    try {
      console.log('Running database migrations...')
      
      // 添加enabled字段
      await this.addEnabledField()
      
      console.log('All migrations completed successfully')
    } catch (error) {
      console.error('Migration failed:', error)
      throw error
    }
  }
}

// 在数据库初始化后自动运行迁移
export async function runDatabaseMigrations() {
  try {
    await DatabaseMigration.runMigrations()
  } catch (error) {
    console.error('Database migration failed:', error)
    // 不抛出错误，避免阻止应用启动
  }
}
