(()=>{var a={};a.id=78,a.ids=[78,329],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},4870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6487:()=>{},6689:a=>{"use strict";a.exports=require("sqlite3")},6710:(a,b,c)=>{"use strict";c.d(b,{DatabaseOperations:()=>l});let d=null,e=null,f=null;async function g(){if(!d)try{d=c(6689).verbose(),e=c(3873),f=c(9021)}catch(a){throw console.error("Failed to load database modules:",a),Error("Database modules not available")}}let h=process.env.DATABASE_URL||"./database/iptv.db";async function i(){if(await g(),e&&f){let a=e.dirname(h);f.existsSync(a)||f.mkdirSync(a,{recursive:!0})}}class j{static instance;db=null;constructor(){}static getInstance(){return j.instance||(j.instance=new j),j.instance}async getConnection(){return await g(),await i(),this.db||(this.db=new d.Database(h,a=>{if(a)throw console.error("数据库连接失败:",a.message),a;console.log("数据库连接成功")}),this.db.run("PRAGMA foreign_keys = ON"),await this.initializeTables()),this.db}async initializeTables(){if(this.db)for(let a of[`CREATE TABLE IF NOT EXISTS sources (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        type TEXT CHECK(type IN ('M3U', 'M3U8', 'URL')) NOT NULL,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        channel_count INTEGER DEFAULT 0,
        status TEXT CHECK(status IN ('active', 'inactive', 'error')) DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,`CREATE TABLE IF NOT EXISTS channels (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        group_name TEXT DEFAULT 'Default',
        logo_url TEXT,
        source_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (source_id) REFERENCES sources (id) ON DELETE CASCADE
      )`,`CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,`CREATE TABLE IF NOT EXISTS play_history (
        id TEXT PRIMARY KEY,
        channel_id TEXT,
        channel_name TEXT NOT NULL,
        played_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        duration INTEGER DEFAULT 0,
        position INTEGER DEFAULT 0,
        FOREIGN KEY (channel_id) REFERENCES channels (id) ON DELETE CASCADE
      )`,`CREATE TABLE IF NOT EXISTS favorites (
        id TEXT PRIMARY KEY,
        channel_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (channel_id) REFERENCES channels (id) ON DELETE CASCADE
      )`,"CREATE INDEX IF NOT EXISTS idx_channels_source_id ON channels (source_id)","CREATE INDEX IF NOT EXISTS idx_channels_group_name ON channels (group_name)","CREATE INDEX IF NOT EXISTS idx_play_history_channel_id ON play_history (channel_id)","CREATE INDEX IF NOT EXISTS idx_play_history_played_at ON play_history (played_at)","CREATE INDEX IF NOT EXISTS idx_favorites_channel_id ON favorites (channel_id)"])await this.run(a)}async run(a,b=[]){let c=await this.getConnection();return new Promise((d,e)=>{c.run(a,b,function(a){a?e(a):d()})})}async get(a,b=[]){let c=await this.getConnection();return new Promise((d,e)=>{c.get(a,b,(a,b)=>{a?e(a):d(b)})})}async all(a,b=[]){let c=await this.getConnection();return new Promise((d,e)=>{c.all(a,b,(a,b)=>{a?e(a):d(b)})})}async close(){if(this.db)return new Promise((a,b)=>{this.db.close(c=>{c?b(c):(this.db=null,a())})})}async testConnection(){try{let a=await this.getConnection();return new Promise(b=>{a.get("SELECT 1",a=>{b(!a)})})}catch(a){return console.error("Database connection test failed:",a),!1}}}let k=j.getInstance();class l{static async createSource(a){let b=`src_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;return await k.run(`INSERT INTO sources (id, name, url, type, last_updated, channel_count, status) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,[b,a.name,a.url,a.type,a.last_updated,a.channel_count,a.status]),b}static async getSources(){return await k.all("SELECT * FROM sources ORDER BY created_at DESC")}static async getSource(a){return await k.get("SELECT * FROM sources WHERE id = ?",[a])}static async updateSource(a,b){let c=Object.keys(b).map(a=>`${a} = ?`).join(", "),d=Object.values(b);await k.run(`UPDATE sources SET ${c}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,[...d,a])}static async deleteSource(a){await k.run("DELETE FROM sources WHERE id = ?",[a])}static async createChannel(a){let b=`ch_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;return await k.run(`INSERT INTO channels (id, name, url, group_name, logo_url, source_id) 
       VALUES (?, ?, ?, ?, ?, ?)`,[b,a.name,a.url,a.group_name,a.logo_url,a.source_id]),b}static async getChannels(a,b){let c="SELECT * FROM channels",d=[];if(a||b){c+=" WHERE";let e=[];a&&(e.push(" source_id = ?"),d.push(a)),b&&(e.push(" group_name = ?"),d.push(b)),c+=e.join(" AND")}return c+=" ORDER BY group_name, name",await k.all(c,d)}static async getChannel(a){return await k.get("SELECT * FROM channels WHERE id = ?",[a])}static async updateChannel(a,b){let c=Object.keys(b).map(a=>`${a} = ?`).join(", "),d=Object.values(b);await k.run(`UPDATE channels SET ${c}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,[...d,a])}static async deleteChannel(a){await k.run("DELETE FROM channels WHERE id = ?",[a])}static async deleteChannelsBySource(a){await k.run("DELETE FROM channels WHERE source_id = ?",[a])}static async getSetting(a){let b=await k.get("SELECT value FROM settings WHERE key = ?",[a]);return b?.value}static async setSetting(a,b){await k.run(`INSERT OR REPLACE INTO settings (key, value, updated_at) 
       VALUES (?, ?, CURRENT_TIMESTAMP)`,[a,b])}static async getSettings(){return(await k.all("SELECT key, value FROM settings")).reduce((a,b)=>(a[b.key]=b.value,a),{})}static async testConnection(){return await k.testConnection()}}},8304:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{GET:()=>w,POST:()=>x});var e=c(6559),f=c(8088),g=c(7719),h=c(6191),i=c(1289),j=c(261),k=c(2603),l=c(9893),m=c(4823),n=c(7220),o=c(6946),p=c(7912),q=c(9786),r=c(6143),s=c(6439),t=c(3365),u=c(2190),v=c(6710);async function w(a){try{let{searchParams:b}=new URL(a.url),c={query:b.get("query")||void 0,group:b.get("group")||void 0,source:b.get("source")||void 0,sortBy:b.get("sortBy")||"name",sortOrder:b.get("sortOrder")||"asc",page:parseInt(b.get("page")||"1"),limit:parseInt(b.get("limit")||"10000")},d=c.source,e=c.group,f=(await v.DatabaseOperations.getChannels(d,e)).map(a=>({id:a.id,name:a.name,url:a.url,group:a.group_name,logo:a.logo_url||void 0,sourceId:a.source_id,createdAt:new Date(a.created_at),updatedAt:new Date(a.updated_at)}));if(c.query){let a=c.query.toLowerCase();f=f.filter(b=>b.name.toLowerCase().includes(a)||b.group.toLowerCase().includes(a))}f.sort((a,b)=>{let d=0;switch(c.sortBy){case"name":d=a.name.localeCompare(b.name);break;case"group":d=a.group.localeCompare(b.group)||a.name.localeCompare(b.name);break;case"createdAt":d=a.createdAt.getTime()-b.createdAt.getTime();break;default:d=0}return"desc"===c.sortOrder?-d:d});let g=(c.page-1)*c.limit,h=g+c.limit,i=f.slice(g,h),j={success:!0,data:{channels:i,pagination:{page:c.page,limit:c.limit,total:f.length,totalPages:Math.ceil(f.length/c.limit)},filters:c}};return u.NextResponse.json(j)}catch(b){console.error("Failed to get channels:",b);let a={success:!1,error:"Failed to get channels",message:b instanceof Error?b.message:"Unknown error"};return u.NextResponse.json(a,{status:500})}}async function x(a){try{let{name:b,url:c,group:d,logo:e,sourceId:f}=await a.json();if(!b||!c)return u.NextResponse.json({success:!1,error:"Missing required fields",message:"Name and URL are required"},{status:400});try{new URL(c)}catch{return u.NextResponse.json({success:!1,error:"Invalid URL",message:"Please provide a valid URL"},{status:400})}if(f&&!await v.DatabaseOperations.getSource(f)){let a={success:!1,error:"Source not found",message:`Source with id ${f} not found`};return u.NextResponse.json(a,{status:404})}let g=await v.DatabaseOperations.createChannel({name:b.trim(),url:c.trim(),group_name:(d||"Default").trim(),logo_url:e?.trim()||null,source_id:f||"manual"}),h=await v.DatabaseOperations.getChannel(g);if(!h)throw Error("Failed to retrieve created channel");let i={id:h.id,name:h.name,url:h.url,group:h.group_name,logo:h.logo_url||void 0,sourceId:h.source_id,createdAt:new Date(h.created_at),updatedAt:new Date(h.updated_at)};return u.NextResponse.json({success:!0,data:i,message:"Channel created successfully"},{status:201})}catch(b){console.error("Failed to create channel:",b);let a={success:!1,error:"Failed to create channel",message:b instanceof Error?b.message:"Unknown error"};return u.NextResponse.json(a,{status:500})}}let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/channels/route",pathname:"/api/channels",filename:"route",bundlePath:"app/api/channels/route"},distDir:".next",projectDir:"",resolvedPagePath:"D:\\Project\\IPTV\\src\\app\\api\\channels\\route.ts",nextConfigOutput:"standalone",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/api/channels/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},8335:()=>{},9021:a=>{"use strict";a.exports=require("fs")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=8304));module.exports=c})();