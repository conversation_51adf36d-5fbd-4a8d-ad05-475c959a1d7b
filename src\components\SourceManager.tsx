'use client'

import React, { useState } from 'react'
import { Plus, Edit, Trash2, RefreshCw, Upload, Link, AlertCircle, CheckCircle, XCircle, Power } from 'lucide-react'
import { Source } from '@/types'
import { cn, formatDate } from '@/lib/utils'

interface SourceManagerProps {
  sources: Source[]
  onSourceAdd: (source: Omit<Source, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  onSourceUpdate: (id: string, source: Partial<Source>) => Promise<void>
  onSourceDelete: (id: string) => Promise<void>
  onSourceRefresh: (id: string) => Promise<void>
  onSourceToggle: (id: string, enabled: boolean) => Promise<void>
  onChannelsRefresh: () => Promise<void>
  loading?: boolean
  className?: string
}

export default function SourceManager({
  sources,
  onSourceAdd,
  onSourceUpdate,
  onSourceDelete,
  onSourceRefresh,
  onSourceToggle,
  onChannelsRefresh,
  loading = false,
  className
}: SourceManagerProps) {
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingSource, setEditingSource] = useState<Source | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    type: 'M3U' as 'M3U' | 'M3U8' | 'URL'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [refreshingIds, setRefreshingIds] = useState<Set<string>>(new Set())
  const [togglingIds, setTogglingIds] = useState<Set<string>>(new Set())

  const resetForm = () => {
    setFormData({ name: '', url: '', type: 'M3U' })
    setShowAddForm(false)
    setEditingSource(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim() || !formData.url.trim()) return

    setIsSubmitting(true)
    try {
      if (editingSource) {
        await onSourceUpdate(editingSource.id, {
          name: formData.name.trim(),
          url: formData.url.trim(),
          type: formData.type
        })
      } else {
        await onSourceAdd({
          name: formData.name.trim(),
          url: formData.url.trim(),
          type: formData.type,
          lastUpdated: new Date(),
          channelCount: 0,
          status: 'active',
          enabled: false
        })
      }
      resetForm()
    } catch (error) {
      console.error('Failed to save source:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEdit = (source: Source) => {
    setFormData({
      name: source.name,
      url: source.url,
      type: source.type
    })
    setEditingSource(source)
    setShowAddForm(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个订阅源吗？这将同时删除所有相关频道。')) return
    
    try {
      await onSourceDelete(id)
    } catch (error) {
      console.error('Failed to delete source:', error)
    }
  }

  const handleRefresh = async (id: string) => {
    setRefreshingIds(prev => new Set(prev).add(id))
    try {
      await onSourceRefresh(id)
    } catch (error) {
      console.error('Failed to refresh source:', error)
    } finally {
      setRefreshingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    }
  }

  const handleToggle = async (id: string, enabled: boolean) => {
    setTogglingIds(prev => new Set(prev).add(id))
    try {
      await onSourceToggle(id, enabled)
      // 如果启用了订阅源，刷新频道列表
      if (enabled) {
        await onChannelsRefresh()
      }
    } catch (error) {
      console.error('Failed to toggle source:', error)
    } finally {
      setTogglingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    }
  }

  const getStatusIcon = (status: Source['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'inactive':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusText = (status: Source['status']) => {
    switch (status) {
      case 'active':
        return '正常'
      case 'error':
        return '错误'
      case 'inactive':
        return '未激活'
      default:
        return '未知'
    }
  }

  return (
    <div className={cn('flex flex-col h-full bg-background', className)}>
      {/* 头部 */}
      <div className="p-4 border-b border-border bg-card/50">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-foreground">
              订阅源管理
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              管理您的IPTV订阅源，支持M3U和M3U8格式
            </p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowAddForm(true)}
              disabled={loading}
              className="flex items-center gap-2 px-6 py-2.5 min-w-[120px] bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm font-medium"
            >
              <Plus className="w-4 h-4" />
              <span>添加</span>
            </button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">

        {/* 添加/编辑表单 */}
        {showAddForm && (
          <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-foreground mb-4">
              {editingSource ? '编辑订阅源' : '添加订阅源'}
            </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                名称
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="请输入订阅源名称"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                URL地址
              </label>
              <input
                type="url"
                value={formData.url}
                onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                placeholder="https://example.com/playlist.m3u"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                类型
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="M3U">M3U</option>
                <option value="M3U8">M3U8</option>
                <option value="URL">URL</option>
              </select>
            </div>

            <div className="flex items-center space-x-3 pt-4">
              <button
                type="submit"
                disabled={isSubmitting || !formData.name.trim() || !formData.url.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {isSubmitting ? '保存中...' : editingSource ? '更新' : '添加'}
              </button>
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 订阅源列表 */}
      <div className="space-y-4">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
              <span className="text-gray-500">加载中...</span>
            </div>
          </div>
        ) : sources.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <Upload className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <div className="text-lg">暂无订阅源</div>
            <div className="text-sm">点击上方按钮添加您的第一个订阅源</div>
          </div>
        ) : (
          sources.map(source => (
            <div
              key={source.id}
              className="bg-card border border-border rounded-lg p-4 shadow-sm"
            >
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-3">
                    <h3 className="font-semibold text-foreground text-lg truncate">
                      {source.name}
                    </h3>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      {getStatusIcon(source.status)}
                      <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full whitespace-nowrap">
                        {getStatusText(source.status)}
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-start gap-2 text-sm text-muted-foreground">
                      <Link className="w-4 h-4 flex-shrink-0 mt-1" />
                      <span className="font-mono text-xs bg-muted px-2 py-1 rounded break-all leading-relaxed">
                        {source.url}
                      </span>
                    </div>
                    <div className="mt-3 space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">类型:</span>
                        <span className="font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-md">
                          {source.type}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">频道数:</span>
                        <span className="font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-md">
                          {source.channelCount} 个
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">更新时间:</span>
                        <span className="font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-md text-xs">
                          {formatDate(source.lastUpdated)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">状态:</span>
                        <span className={cn(
                          'font-medium px-2 py-1 rounded-md text-xs',
                          source.enabled
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                            : 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300'
                        )}>
                          {source.enabled ? '已启用' : '已禁用'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-1 flex-shrink-0">
                  {/* 启用/禁用切换按钮 */}
                  <button
                    onClick={() => handleToggle(source.id, !source.enabled)}
                    disabled={togglingIds.has(source.id)}
                    className={cn(
                      'p-2 rounded-md transition-all duration-200',
                      source.enabled
                        ? 'text-green-600 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30'
                        : 'text-muted-foreground hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20'
                    )}
                    title={source.enabled ? '禁用订阅源' : '启用订阅源'}
                  >
                    <Power className={cn(
                      'w-4 h-4',
                      togglingIds.has(source.id) && 'animate-pulse'
                    )} />
                  </button>
                  <button
                    onClick={() => handleRefresh(source.id)}
                    disabled={refreshingIds.has(source.id)}
                    className="p-2 text-muted-foreground hover:text-primary hover:bg-accent rounded-md transition-all duration-200"
                    title="刷新"
                  >
                    <RefreshCw className={cn(
                      'w-4 h-4',
                      refreshingIds.has(source.id) && 'animate-spin'
                    )} />
                  </button>
                  <button
                    onClick={() => handleEdit(source)}
                    className="p-2 text-muted-foreground hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-md transition-all duration-200"
                    title="编辑"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(source.id)}
                    className="p-2 text-muted-foreground hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200"
                    title="删除"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      </div>
    </div>
  )
}
