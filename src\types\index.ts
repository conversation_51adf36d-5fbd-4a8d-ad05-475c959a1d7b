// 频道相关类型
export interface Channel {
  id: string
  name: string
  url: string
  group: string
  logo?: string
  sourceId: string
  createdAt: Date
  updatedAt: Date
}

// 订阅源相关类型
export interface Source {
  id: string
  name: string
  url: string
  type: 'M3U' | 'M3U8' | 'URL'
  lastUpdated: Date
  channelCount: number
  status: 'active' | 'inactive' | 'error'
  enabled: boolean
  createdAt: Date
  updatedAt: Date
}

// 播放器相关类型
export interface PlayerConfig {
  url: string
  title: string
  isLive?: boolean
  poster?: string
  autoplay?: boolean
  muted?: boolean
  volume?: number
  loop?: boolean
  controls?: boolean
  fullscreen?: boolean
  pip?: boolean
  screenshot?: boolean
  hotkey?: boolean
  miniProgressBar?: boolean
  mutex?: boolean
  backdrop?: boolean
  playsInline?: boolean
  autoSize?: boolean
  autoMini?: boolean
  setting?: boolean
  flip?: boolean
  playbackRate?: boolean
  aspectRatio?: boolean
  subtitleOffset?: boolean
}

// M3U解析相关类型
export interface M3UEntry {
  duration: number
  title: string
  url: string
  attributes: Record<string, string>
  group?: string
  logo?: string
}

export interface ParsedM3U {
  entries: M3UEntry[]
  metadata: {
    totalEntries: number
    groups: string[]
    parseTime: Date
  }
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 搜索和过滤类型
export interface SearchFilters {
  query?: string
  group?: string
  source?: string
  sortBy?: 'name' | 'group' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// 用户设置类型
export interface UserSettings {
  theme: 'light' | 'dark'
  language: string
  autoplay: boolean
  volume: number
  quality: 'auto' | 'high' | 'medium' | 'low'
  subtitles: boolean
  fullscreenOnPlay: boolean
  rememberPosition: boolean
  startMuted: boolean
}

// 播放历史类型
export interface PlayHistory {
  id: string
  channelId: string
  channelName: string
  playedAt: Date
  duration: number
  position: number
}

// 收藏类型
export interface Favorite {
  id: string
  channelId: string
  createdAt: Date
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
}

// 组件Props类型
export interface ChannelListProps {
  channels: Channel[]
  onChannelSelect: (channel: Channel) => void
  selectedChannel?: Channel
  loading?: boolean
  error?: string
}

export interface PlayerProps {
  channel?: Channel
  config?: Partial<PlayerConfig>
  onError?: (error: AppError) => void
  onReady?: () => void
}

export interface SourceManagerProps {
  sources: Source[]
  onSourceAdd: (source: Omit<Source, 'id' | 'createdAt' | 'updatedAt'>) => void
  onSourceUpdate: (id: string, source: Partial<Source>) => void
  onSourceDelete: (id: string) => void
  loading?: boolean
}

// 数据库相关类型
export interface DatabaseChannel {
  id: string
  name: string
  url: string
  group_name: string
  logo_url?: string
  source_id: string
  created_at: string
  updated_at: string
}

export interface DatabaseSource {
  id: string
  name: string
  url: string
  type: string
  last_updated: string
  channel_count: number
  status: string
  enabled: number
  created_at: string
  updated_at: string
}

export interface DatabaseSettings {
  key: string
  value: string
}

// 主题相关类型
export type Theme = 'light' | 'dark' | 'system'

// 布局相关类型
export interface LayoutProps {
  children: React.ReactNode
  sidebar?: boolean
  header?: boolean
  footer?: boolean
}

// 导航相关类型
export interface NavItem {
  label: string
  href: string
  icon?: React.ComponentType
  active?: boolean
}

// 统计相关类型
export interface Statistics {
  totalChannels: number
  totalSources: number
  activeChannels: number
  totalPlayTime: number
  favoriteChannels: number
  recentlyPlayed: number
}
