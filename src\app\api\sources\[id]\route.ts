import { NextRequest, NextResponse } from 'next/server'
import { DatabaseOperations } from '@/lib/database'
import { M3UParser } from '@/lib/m3u-parser'
import { ApiResponse, Source } from '@/types'

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

// GET /api/sources/[id] - 获取单个订阅源
export async function GET(_request: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params
    const source = await DatabaseOperations.getSource(resolvedParams.id)
    
    if (!source) {
      const response: ApiResponse = {
        success: false,
        error: 'Source not found',
        message: `Source with id ${resolvedParams.id} not found`
      }
      return NextResponse.json(response, { status: 404 })
    }

    const apiSource: Source = {
      id: source.id,
      name: source.name,
      url: source.url,
      type: source.type as 'M3U' | 'M3U8' | 'URL',
      lastUpdated: new Date(source.last_updated),
      channelCount: source.channel_count,
      status: source.status as 'active' | 'inactive' | 'error',
      enabled: <PERSON><PERSON><PERSON>(source.enabled),
      createdAt: new Date(source.created_at),
      updatedAt: new Date(source.updated_at)
    }

    const response: ApiResponse<Source> = {
      success: true,
      data: apiSource
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Failed to get source:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get source',
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT /api/sources/[id] - 更新订阅源
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params
    const body = await request.json()
    const { name, url, type } = body

    // 检查源是否存在
    const existingSource = await DatabaseOperations.getSource(resolvedParams.id)
    if (!existingSource) {
      const response: ApiResponse = {
        success: false,
        error: 'Source not found',
        message: `Source with id ${resolvedParams.id} not found`
      }
      return NextResponse.json(response, { status: 404 })
    }

    // 验证输入
    if (name !== undefined && !name.trim()) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid name',
        message: 'Name cannot be empty'
      }
      return NextResponse.json(response, { status: 400 })
    }

    if (url !== undefined) {
      try {
        new URL(url)
      } catch {
        const response: ApiResponse = {
          success: false,
          error: 'Invalid URL',
          message: 'Please provide a valid URL'
        }
        return NextResponse.json(response, { status: 400 })
      }
    }

    // 准备更新数据
    const updates: any = {
      updated_at: new Date().toISOString()
    }

    if (name !== undefined) {
      updates.name = name.trim()
    }
    if (url !== undefined) {
      updates.url = url.trim()
    }
    if (type !== undefined) {
      updates.type = type
    }

    // 更新源
    await DatabaseOperations.updateSource(resolvedParams.id, updates)

    // 如果URL或类型发生变化，重新解析频道
    if (url !== undefined || type !== undefined) {
      try {
        const finalUrl = url || existingSource.url
        const finalType = type || existingSource.type
        await parseAndAddChannels(resolvedParams.id, finalUrl, finalType)
      } catch (parseError) {
        console.warn('Failed to parse M3U content:', parseError)
        await DatabaseOperations.updateSource(resolvedParams.id, {
          status: 'error',
          last_updated: new Date().toISOString()
        })
      }
    }

    // 获取更新后的源
    const updatedSource = await DatabaseOperations.getSource(resolvedParams.id)
    if (!updatedSource) {
      throw new Error('Failed to retrieve updated source')
    }

    const apiSource: Source = {
      id: updatedSource.id,
      name: updatedSource.name,
      url: updatedSource.url,
      type: updatedSource.type as 'M3U' | 'M3U8' | 'URL',
      lastUpdated: new Date(updatedSource.last_updated),
      channelCount: updatedSource.channel_count,
      status: updatedSource.status as 'active' | 'inactive' | 'error',
      createdAt: new Date(updatedSource.created_at),
      updatedAt: new Date(updatedSource.updated_at)
    }

    const response: ApiResponse<Source> = {
      success: true,
      data: apiSource,
      message: 'Source updated successfully'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Failed to update source:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Failed to update source',
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE /api/sources/[id] - 删除订阅源
export async function DELETE(_request: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params
    // 检查源是否存在
    const existingSource = await DatabaseOperations.getSource(resolvedParams.id)
    if (!existingSource) {
      const response: ApiResponse = {
        success: false,
        error: 'Source not found',
        message: `Source with id ${resolvedParams.id} not found`
      }
      return NextResponse.json(response, { status: 404 })
    }

    // 删除源（会级联删除相关频道）
    await DatabaseOperations.deleteSource(resolvedParams.id)

    const response: ApiResponse = {
      success: true,
      message: 'Source deleted successfully'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Failed to delete source:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Failed to delete source',
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// 解析M3U内容并添加频道
async function parseAndAddChannels(sourceId: string, url: string, type: string): Promise<void> {
  if (type !== 'M3U' && type !== 'M3U8') {
    return // 只处理M3U格式
  }

  // 获取M3U内容
  const content = await M3UParser.fetchFromUrl(url)
  
  // 验证内容
  const validation = M3UParser.validate(content)
  if (!validation.valid) {
    throw new Error(`Invalid M3U content: ${validation.errors.join(', ')}`)
  }

  // 解析内容
  const parsed = M3UParser.parse(content)
  
  // 删除该源的现有频道
  await DatabaseOperations.deleteChannelsBySource(sourceId)
  
  // 添加新频道
  let addedCount = 0
  for (const entry of parsed.entries) {
    try {
      await DatabaseOperations.createChannel({
        name: entry.title,
        url: entry.url,
        group_name: entry.group || 'Default',
        logo_url: entry.logo,
        source_id: sourceId
      })
      addedCount++
    } catch (error) {
      console.warn(`Failed to add channel ${entry.title}:`, error)
    }
  }

  // 更新源的频道数量和状态
  await DatabaseOperations.updateSource(sourceId, {
    channel_count: addedCount,
    status: addedCount > 0 ? 'active' : 'error',
    last_updated: new Date().toISOString()
  })
}
