1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[894,[],"ClientPageRoot"]
5:I[314,["277","static/chunks/277-19c98c21922e4614.js","225","static/chunks/225-ebe432ec84bae648.js","623","static/chunks/623-c7cfd23b86a55623.js","974","static/chunks/app/page-8e0815cb06e8adf9.js"],"default"]
8:I[9665,[],"OutletBoundary"]
a:I[4911,[],"AsyncMetadataOutlet"]
c:I[9665,[],"ViewportBoundary"]
e:I[9665,[],"MetadataBoundary"]
f:"$Sreact.suspense"
11:I[8393,[],""]
:HL["/_next/static/css/c96a70815802e29c.css","style"]
0:{"P":null,"b":"SQOnT45q9WTA5Ww9sOJn3","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/c96a70815802e29c.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"zh-CN","suppressHydrationWarning":true,"children":[["$","head",null,{"children":[["$","link",null,{"rel":"icon","href":"/favicon.ico"}],["$","link",null,{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","link",null,{"rel":"manifest","href":"/manifest.json"}],["$","meta",null,{"name":"theme-color","content":"#000000"}],["$","meta",null,{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"apple-mobile-web-app-status-bar-style","content":"black-translucent"}],["$","meta",null,{"name":"apple-mobile-web-app-title","content":"IPTV Player"}],["$","meta",null,{"name":"mobile-web-app-capable","content":"yes"}],["$","meta",null,{"name":"msapplication-TileColor","content":"#000000"}],["$","meta",null,{"name":"msapplication-config","content":"/browserconfig.xml"}]]}],["$","body",null,{"className":"__className_e8ce0c","suppressHydrationWarning":true,"children":[["$","div",null,{"id":"root","className":"min-h-screen bg-white dark:bg-gray-900 transition-colors","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","div",null,{"id":"modal-root"}],["$","div",null,{"id":"toast-root"}]]}]]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L4",null,{"Component":"$5","searchParams":{},"params":{},"promises":["$@6","$@7"]}],null,["$","$L8",null,{"children":["$L9",["$","$La",null,{"promise":"$@b"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Lc",null,{"children":"$Ld"}],null],["$","$Le",null,{"children":["$","div",null,{"hidden":true,"children":["$","$f",null,{"fallback":null,"children":"$L10"}]}]}]]}],false]],"m":"$undefined","G":["$11",[]],"s":false,"S":true}
6:{}
7:"$0:f:0:1:2:children:1:props:children:0:props:params"
d:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
9:null
b:{"metadata":[["$","title","0",{"children":"IPTV Player - 现代化的IPTV网页播放器"}],["$","meta","1",{"name":"description","content":"支持HLS直播流播放、M3U/M3U8订阅源管理的现代化IPTV播放器"}],["$","meta","2",{"name":"author","content":"IPTV Player Team"}],["$","meta","3",{"name":"keywords","content":"IPTV,播放器,HLS,M3U8,直播,流媒体"}],["$","meta","4",{"name":"creator","content":"IPTV Player Team"}],["$","meta","5",{"name":"publisher","content":"IPTV Player"}],["$","meta","6",{"name":"robots","content":"noindex, nofollow"}],["$","meta","7",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","8",{"property":"og:title","content":"IPTV Player"}],["$","meta","9",{"property":"og:description","content":"现代化的IPTV网页播放器"}],["$","meta","10",{"property":"og:site_name","content":"IPTV Player"}],["$","meta","11",{"property":"og:locale","content":"zh_CN"}],["$","meta","12",{"property":"og:type","content":"website"}],["$","meta","13",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","14",{"name":"twitter:title","content":"IPTV Player"}],["$","meta","15",{"name":"twitter:description","content":"现代化的IPTV网页播放器"}]],"error":null,"digest":"$undefined"}
10:"$b:metadata"
