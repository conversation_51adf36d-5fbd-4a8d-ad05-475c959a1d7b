import { NextRequest, NextResponse } from 'next/server'
import { DatabaseOperations } from '@/lib/database'
import { M3UParser } from '@/lib/m3u-parser'
import { ApiResponse, Source } from '@/types'
import { validateSourceData, isValidContentType } from '@/lib/validation'

// GET /api/sources - 获取所有订阅源
export async function GET() {
  try {
    const sources = await DatabaseOperations.getSources()
    
    // 转换数据库格式到API格式
    const apiSources: Source[] = sources.map(source => ({
      id: source.id,
      name: source.name,
      url: source.url,
      type: source.type as 'M3U' | 'M3U8' | 'URL',
      lastUpdated: new Date(source.last_updated),
      channelCount: source.channel_count,
      status: source.status as 'active' | 'inactive' | 'error',
      enabled: Boolean(source.enabled),
      createdAt: new Date(source.created_at),
      updatedAt: new Date(source.updated_at)
    }))

    const response: ApiResponse<Source[]> = {
      success: true,
      data: apiSources
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Failed to get sources:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get sources',
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST /api/sources - 添加新的订阅源
export async function POST(request: NextRequest) {
  try {
    // 验证Content-Type
    const contentType = request.headers.get('content-type')
    if (!isValidContentType(contentType)) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid content type',
        message: 'Content-Type must be application/json'
      }
      return NextResponse.json(response, { status: 400 })
    }

    const body = await request.json()

    // 使用简化的验证
    const validation = validateSourceData(body)
    if (!validation.valid) {
      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        message: validation.errors.join(', ')
      }
      return NextResponse.json(response, { status: 400 })
    }

    const { name, url, type } = body

    // 验证URL格式
    try {
      new URL(url)
    } catch {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid URL',
        message: 'Please provide a valid URL'
      }
      return NextResponse.json(response, { status: 400 })
    }

    // 创建订阅源
    const sourceId = await DatabaseOperations.createSource({
      name: name.trim(),
      url: url.trim(),
      type,
      last_updated: new Date().toISOString(),
      channel_count: 0,
      status: 'active',
      enabled: 0 // 默认不启用，需要用户手动启用
    })

    // 尝试解析M3U内容并添加频道
    try {
      await parseAndAddChannels(sourceId, url, type)
    } catch (parseError) {
      console.warn('Failed to parse M3U content:', parseError)
      // 更新源状态为错误，但不删除源
      await DatabaseOperations.updateSource(sourceId, {
        status: 'error',
        last_updated: new Date().toISOString()
      })
    }

    // 获取创建的源
    const createdSource = await DatabaseOperations.getSource(sourceId)
    if (!createdSource) {
      throw new Error('Failed to retrieve created source')
    }

    const apiSource: Source = {
      id: createdSource.id,
      name: createdSource.name,
      url: createdSource.url,
      type: createdSource.type as 'M3U' | 'M3U8' | 'URL',
      lastUpdated: new Date(createdSource.last_updated),
      channelCount: createdSource.channel_count,
      status: createdSource.status as 'active' | 'inactive' | 'error',
      enabled: Boolean(createdSource.enabled),
      createdAt: new Date(createdSource.created_at),
      updatedAt: new Date(createdSource.updated_at)
    }

    const response: ApiResponse<Source> = {
      success: true,
      data: apiSource,
      message: 'Source created successfully'
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Failed to create source:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create source',
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// 解析M3U内容并添加频道
async function parseAndAddChannels(sourceId: string, url: string, type: string): Promise<void> {
  if (type !== 'M3U' && type !== 'M3U8') {
    return // 只处理M3U格式
  }

  // 获取M3U内容
  const content = await M3UParser.fetchFromUrl(url)
  
  // 验证内容
  const validation = M3UParser.validate(content)
  if (!validation.valid) {
    throw new Error(`Invalid M3U content: ${validation.errors.join(', ')}`)
  }

  // 解析内容
  const parsed = M3UParser.parse(content)
  
  // 删除该源的现有频道
  await DatabaseOperations.deleteChannelsBySource(sourceId)
  
  // 添加新频道
  let addedCount = 0
  for (const entry of parsed.entries) {
    try {
      await DatabaseOperations.createChannel({
        name: entry.title,
        url: entry.url,
        group_name: entry.group || 'Default',
        logo_url: entry.logo,
        source_id: sourceId
      })
      addedCount++
    } catch (error) {
      console.warn(`Failed to add channel ${entry.title}:`, error)
    }
  }

  // 更新源的频道数量和状态
  await DatabaseOperations.updateSource(sourceId, {
    channel_count: addedCount,
    status: addedCount > 0 ? 'active' : 'error',
    last_updated: new Date().toISOString()
  })
}
