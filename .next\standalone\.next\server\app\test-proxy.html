<!DOCTYPE html><!--SQOnT45q9WTA5Ww9sOJn3--><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/c96a70815802e29c.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-e6ce8e83dbbdf6c1.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-e5f5bf07ef48f1a6.js" async=""></script><script src="/_next/static/chunks/main-app-93436cb709c3e062.js" async=""></script><script src="/_next/static/chunks/app/test-proxy/page-8caf085afdf77298.js" async=""></script><link rel="icon" href="/favicon.ico"/><link rel="apple-touch-icon" href="/apple-touch-icon.png"/><link rel="manifest" href="/manifest.json"/><meta name="theme-color" content="#000000"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/><meta name="apple-mobile-web-app-title" content="IPTV Player"/><meta name="mobile-web-app-capable" content="yes"/><meta name="msapplication-TileColor" content="#000000"/><meta name="msapplication-config" content="/browserconfig.xml"/><title>IPTV Player - 现代化的IPTV网页播放器</title><meta name="description" content="支持HLS直播流播放、M3U/M3U8订阅源管理的现代化IPTV播放器"/><meta name="author" content="IPTV Player Team"/><meta name="keywords" content="IPTV,播放器,HLS,M3U8,直播,流媒体"/><meta name="creator" content="IPTV Player Team"/><meta name="publisher" content="IPTV Player"/><meta name="robots" content="noindex, nofollow"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="IPTV Player"/><meta property="og:description" content="现代化的IPTV网页播放器"/><meta property="og:site_name" content="IPTV Player"/><meta property="og:locale" content="zh_CN"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="IPTV Player"/><meta name="twitter:description" content="现代化的IPTV网页播放器"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div hidden=""><!--$--><!--/$--></div><div id="root" class="min-h-screen bg-white dark:bg-gray-900 transition-colors"><div class="container mx-auto p-4 max-w-4xl"><h1 class="text-2xl font-bold mb-6">CORS Proxy Test</h1><div class="space-y-4"><div><label for="url" class="block text-sm font-medium mb-2">Test URL (e.g., IPTV stream URL):</label><input id="url" type="url" placeholder="http://example.com/stream.m3u8" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value=""/></div><button disabled="" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed">Test Proxy</button><div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-md"><h3 class="text-lg font-medium text-yellow-800 mb-2">How it works:</h3><ul class="text-sm text-yellow-700 space-y-1"><li>• The proxy server fetches content from external URLs</li><li>• Adds proper CORS headers to allow browser access</li><li>• Handles both regular HTTP requests and streaming content</li><li>• Automatically detects and handles m3u8 playlist files</li></ul></div><div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md"><h3 class="text-lg font-medium text-blue-800 mb-2">Example URLs to test:</h3><ul class="text-sm text-blue-700 space-y-1"><li>• Local network: http://192.168.x.x/stream.m3u8</li><li>• Public streams: https://example.com/live/stream.m3u8</li><li>• Any HTTP/HTTPS URL that returns content</li></ul></div></div></div><!--$--><!--/$--></div><div id="modal-root"></div><div id="toast-root"></div><script src="/_next/static/chunks/webpack-e6ce8e83dbbdf6c1.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[1840,[\"750\",\"static/chunks/app/test-proxy/page-8caf085afdf77298.js\"],\"default\"]\n8:I[9665,[],\"OutletBoundary\"]\na:I[4911,[],\"AsyncMetadataOutlet\"]\nc:I[9665,[],\"ViewportBoundary\"]\ne:I[9665,[],\"MetadataBoundary\"]\nf:\"$Sreact.suspense\"\n11:I[8393,[],\"\"]\n:HL[\"/_next/static/css/c96a70815802e29c.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"SQOnT45q9WTA5Ww9sOJn3\",\"p\":\"\",\"c\":[\"\",\"test-proxy\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"test-proxy\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/c96a70815802e29c.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"href\":\"/apple-touch-icon.png\"}],[\"$\",\"link\",null,{\"rel\":\"manifest\",\"href\":\"/manifest.json\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#000000\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"black-translucent\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-title\",\"content\":\"IPTV Player\"}],[\"$\",\"meta\",null,{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"msapplication-TileColor\",\"content\":\"#000000\"}],[\"$\",\"meta\",null,{\"name\":\"msapplication-config\",\"content\":\"/browserconfig.xml\"}]]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"div\",null,{\"id\":\"root\",\"className\":\"min-h-screen bg-white dark:bg-gray-900 transition-colors\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"div\",null,{\"id\":\"modal-root\"}],[\"$\",\"div\",null,{\"id\":\"toast-root\"}]]}]]}]]}],{\"children\":[\"test-proxy\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",[\"$\",\"$La\",null,{\"promise\":\"$@b\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],null],[\"$\",\"$Le\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":\"$L10\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"6:{}\n7:\"$0:f:0:1:2:children:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"b:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"IPTV Player - 现代化的IPTV网页播放器\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"支持HLS直播流播放、M3U/M3U8订阅源管理的现代化IPTV播放器\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"IPTV Player Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"IPTV,播放器,HLS,M3U8,直播,流媒体\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"IPTV Player Team\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"IPTV Player\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"noindex, nofollow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:title\",\"content\":\"IPTV Player\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:description\",\"content\":\"现代化的IPTV网页播放器\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:site_name\",\"content\":\"IPTV Player\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:locale\",\"content\":\"zh_CN\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:title\",\"content\":\"IPTV Player\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:description\",\"content\":\"现代化的IPTV网页播放器\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"10:\"$b:metadata\"\n"])</script></body></html>