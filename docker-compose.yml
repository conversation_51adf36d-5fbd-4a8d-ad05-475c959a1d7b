# version: '3.8' - 已废弃，移除以避免警告

services:
  # IPTV播放器应用
  iptv-player:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: iptv-player
    restart: unless-stopped
    network_mode: "host"
    environment:
      # 基本配置
      - NODE_ENV=production
      - NEXT_PUBLIC_APP_NAME=IPTV Player
      - NEXT_PUBLIC_APP_VERSION=1.0.0
      - DATABASE_URL=/app/database/iptv.db
      - PORT=9004

      # 可选配置
      - NEXT_PUBLIC_PLAYER_DEBUG=${NEXT_PUBLIC_PLAYER_DEBUG:-false}
    volumes:
      # 数据持久化
      - ./data:/app/database
      # 日志目录（可选）
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9004/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s