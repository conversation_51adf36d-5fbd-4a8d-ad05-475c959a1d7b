#!/bin/bash

# Docker入口脚本 - 处理权限和启动应用

set -e

echo "=== IPTV Player Docker 入口脚本 ==="
echo "当前用户: $(whoami)"
echo "当前工作目录: $(pwd)"
echo "用户ID: $(id -u)"
echo "组ID: $(id -g)"

# 确保数据库目录存在并有正确权限
echo "检查数据库目录权限..."
if [ -d "/app/database" ]; then
    echo "数据库目录存在: /app/database"
    
    # 检查目录权限
    ls -la /app/database/ || echo "目录为空或无法访问"
    
    # 尝试创建测试文件来验证权限
    if touch /app/database/.test 2>/dev/null; then
        echo "✅ 数据库目录权限正常"
        rm -f /app/database/.test
    else
        echo "❌ 数据库目录权限不足，尝试修复..."
        # 如果是root用户，修复权限
        if [ "$(id -u)" = "0" ]; then
            chown -R nextjs:nodejs /app/database
            chmod -R 755 /app/database
            echo "权限已修复"
        else
            echo "⚠️  非root用户，无法修复权限"
        fi
    fi
else
    echo "创建数据库目录..."
    mkdir -p /app/database
    if [ "$(id -u)" = "0" ]; then
        chown -R nextjs:nodejs /app/database
        chmod -R 755 /app/database
    fi
fi

# 检查日志目录
if [ -d "/app/logs" ]; then
    echo "日志目录存在: /app/logs"
    if [ "$(id -u)" = "0" ]; then
        chown -R nextjs:nodejs /app/logs
        chmod -R 755 /app/logs
    fi
else
    echo "创建日志目录..."
    mkdir -p /app/logs
    if [ "$(id -u)" = "0" ]; then
        chown -R nextjs:nodejs /app/logs
        chmod -R 755 /app/logs
    fi
fi

# 如果当前是root用户，切换到nextjs用户
if [ "$(id -u)" = "0" ]; then
    echo "从root用户切换到nextjs用户..."
    exec su-exec nextjs "$0" "$@"
fi

echo "=== 启动 IPTV Player 应用 ==="
echo "最终用户: $(whoami)"
echo "最终工作目录: $(pwd)"

# 启动Node.js应用
exec node server.js
