# =========================================
# Stage 1: Dependencies Installation
# =========================================
FROM node:18-alpine AS deps

# 安装必要的系统依赖
RUN apk add --no-cache libc6-compat

# 设置工作目录
WORKDIR /app

# 复制包管理文件
COPY package.json package-lock.json* ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# =========================================
# Stage 2: Build Application
# =========================================
FROM node:18-alpine AS builder

# 安装构建依赖
RUN apk add --no-cache libc6-compat python3 make g++

WORKDIR /app

# 复制依赖和源码
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 安装开发依赖用于构建
RUN npm ci

# 设置环境变量
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# 构建应用
RUN npm run build

# =========================================
# Stage 3: Production Runtime
# =========================================
FROM node:18-alpine AS runner

# 安装运行时依赖
RUN apk add --no-cache \
    sqlite \
    curl \
    su-exec \
    && addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=9004
ENV HOSTNAME="0.0.0.0"

# 复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# 创建数据库目录并设置权限
RUN mkdir -p /app/database && chown -R nextjs:nodejs /app/database && chmod -R 755 /app/database

# 复制数据库初始化脚本
COPY --chown=nextjs:nodejs scripts/init-db.js ./scripts/

# 创建简单的启动脚本
RUN echo '#!/bin/sh' > /start.sh && \
    echo 'set -e' >> /start.sh && \
    echo 'echo "=== IPTV Player 启动 ==="' >> /start.sh && \
    echo 'echo "当前用户: $(whoami)"' >> /start.sh && \
    echo 'echo "工作目录: $(pwd)"' >> /start.sh && \
    echo '# 确保数据库目录权限' >> /start.sh && \
    echo 'if [ "$(id -u)" = "0" ]; then' >> /start.sh && \
    echo '  echo "修复数据库目录权限..."' >> /start.sh && \
    echo '  chown -R nextjs:nodejs /app/database 2>/dev/null || true' >> /start.sh && \
    echo '  chmod -R 755 /app/database 2>/dev/null || true' >> /start.sh && \
    echo '  echo "切换到nextjs用户..."' >> /start.sh && \
    echo '  exec su-exec nextjs node server.js' >> /start.sh && \
    echo 'else' >> /start.sh && \
    echo '  echo "以当前用户启动应用..."' >> /start.sh && \
    echo '  exec node server.js' >> /start.sh && \
    echo 'fi' >> /start.sh && \
    chmod +x /start.sh

# 暴露端口
EXPOSE 9004

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:9004/api/health || exit 1

# 启动应用
CMD ["/start.sh"]
