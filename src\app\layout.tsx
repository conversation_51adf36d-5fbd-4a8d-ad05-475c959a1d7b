import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
// 初始化数据库
import '@/lib/database-init'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'IPTV Player - 现代化的IPTV网页播放器',
  description: '支持HLS直播流播放、M3U/M3U8订阅源管理的现代化IPTV播放器',
  keywords: ['IPTV', '播放器', 'HLS', 'M3U8', '直播', '流媒体'],
  authors: [{ name: 'IPTV Player Team' }],
  creator: 'IPTV Player Team',
  publisher: 'IPTV Player',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXTAUTH_URL || 'http://localhost:3000'),
  openGraph: {
    title: 'IPTV Player',
    description: '现代化的IPTV网页播放器',
    type: 'website',
    locale: 'zh_CN',
    siteName: 'IPTV Player',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'IPTV Player',
    description: '现代化的IPTV网页播放器',
  },
  robots: {
    index: false,
    follow: false,
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#000000" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="IPTV Player" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <div id="root" className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
          {children}
        </div>
        <div id="modal-root" />
        <div id="toast-root" />
      </body>
    </html>
  )
}
