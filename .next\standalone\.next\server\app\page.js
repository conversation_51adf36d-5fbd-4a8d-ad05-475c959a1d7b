(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Project\\\\IPTV\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Project\\IPTV\\src\\app\\page.tsx","default")},2469:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>P});var d=c(687),e=c(3210),f=c.n(e);let g=(...a)=>a.filter((a,b,c)=>!!a&&c.indexOf(a)===b).join(" ");var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,e.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:f="",children:i,iconNode:j,...k},l)=>(0,e.createElement)("svg",{ref:l,...h,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:g("lucide",f),...k},[...j.map(([a,b])=>(0,e.createElement)(a,b)),...Array.isArray(i)?i:[i]])),j=(a,b)=>{let c=(0,e.forwardRef)(({className:c,...d},f)=>(0,e.createElement)(i,{ref:f,iconNode:b,className:g(`lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,c),...d}));return c.displayName=`${a}`,c},k=j("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),l=j("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),m=j("Tv",[["rect",{width:"20",height:"15",x:"2",y:"7",rx:"2",ry:"2",key:"10ag99"}],["polyline",{points:"17 2 12 7 7 2",key:"11pgbg"}]]),n=j("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),o=j("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),p=j("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),q=j("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),r=j("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var s=c(5371);let t=j("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),u=j("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),v=j("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var w=c(4780);function x({channels:a,onChannelSelect:b,selectedChannel:c,loading:f=!1,error:g,className:h}){let[i,j]=(0,e.useState)(""),[k,l]=(0,e.useState)("all"),[m,n]=(0,e.useState)("list"),[o,p]=(0,e.useState)("name"),[q,r]=(0,e.useState)(!1),[s,x]=(0,e.useState)(new Set),[y,z]=(0,e.useState)(new Set),A=(0,e.useMemo)(()=>Array.from(new Set(a.map(a=>a.group))).sort(),[a]),B=(0,e.useMemo)(()=>{let b=a;if(i){let a=i.toLowerCase();b=b.filter(b=>b.name.toLowerCase().includes(a)||b.group.toLowerCase().includes(a))}return"all"!==k&&(b=b.filter(a=>a.group===k)),q&&(b=b.filter(a=>s.has(a.id))),b.sort((a,b)=>{let c=a.group.localeCompare(b.group);if(0!==c)return c;switch(o){case"name":return a.name.localeCompare(b.name);case"recent":return new Date(b.updatedAt).getTime()-new Date(a.updatedAt).getTime();default:return 0}}),b},[a,i,k,o,q,s]),C=(0,e.useMemo)(()=>{let a={};return B.forEach(b=>{a[b.group]||(a[b.group]=[]),a[b.group].push(b)}),a},[B]);return f?(0,d.jsx)("div",{className:(0,w.cn)("flex items-center justify-center h-64",h),children:(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,d.jsx)("div",{className:"w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"}),(0,d.jsx)("span",{className:"text-sm text-gray-500",children:"加载频道列表..."})]})}):g?(0,d.jsx)("div",{className:(0,w.cn)("flex items-center justify-center h-64",h),children:(0,d.jsxs)("div",{className:"text-center text-red-500",children:[(0,d.jsx)("div",{className:"text-lg font-medium",children:"加载失败"}),(0,d.jsx)("div",{className:"text-sm opacity-75",children:g})]})}):(0,d.jsxs)("div",{className:(0,w.cn)("flex flex-col h-full bg-background",h),children:[(0,d.jsxs)("div",{className:"p-4 border-b border-border bg-card/50 space-y-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(t,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"}),(0,d.jsx)("input",{type:"text",placeholder:"搜索频道...",value:i,onChange:a=>j(a.target.value),className:"w-full pl-10 pr-4 py-2.5 border border-input rounded-lg bg-background text-foreground placeholder-muted-foreground focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 shadow-sm"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between gap-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("select",{value:k,onChange:a=>l(a.target.value),className:"px-3 py-2 text-sm border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 shadow-sm min-w-[100px]",children:[(0,d.jsx)("option",{value:"all",children:"所有分组"}),A.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]}),(0,d.jsxs)("select",{value:o,onChange:a=>p(a.target.value),className:"px-3 py-2 text-sm border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 shadow-sm min-w-[100px]",children:[(0,d.jsx)("option",{value:"name",children:"按名称"}),(0,d.jsx)("option",{value:"recent",children:"按更新时间"})]})]}),(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsx)("button",{onClick:()=>r(!q),className:(0,w.cn)("p-2 rounded-md transition-all duration-200 shadow-sm",q?"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/50 dark:text-yellow-400 ring-1 ring-yellow-200 dark:ring-yellow-800":"text-muted-foreground hover:text-foreground hover:bg-accent"),title:q?"显示所有频道":"只显示收藏",children:(0,d.jsx)(u,{className:(0,w.cn)("w-4 h-4",q&&"fill-current")})})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground bg-muted/30 px-3 py-2 rounded-md",children:[(0,d.jsxs)("span",{children:["共 ",(0,d.jsx)("span",{className:"font-medium text-foreground",children:B.length})," 个频道",i&&(0,d.jsxs)("span",{className:"ml-1",children:['\xb7 搜索 "',(0,d.jsx)("span",{className:"font-medium text-foreground",children:i}),'"']}),"all"!==k&&(0,d.jsxs)("span",{className:"ml-1",children:['\xb7 分组 "',(0,d.jsx)("span",{className:"font-medium text-foreground",children:k}),'"']})]}),q&&(0,d.jsx)("span",{className:"text-yellow-600 dark:text-yellow-400 font-medium",children:"仅收藏"})]})]}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto",children:0===B.length?(0,d.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-lg",children:"没有找到频道"}),(0,d.jsx)("div",{className:"text-sm",children:"请尝试调整搜索条件"})]})}):(0,d.jsx)("div",{className:"divide-y divide-border/50",children:Object.entries(C).map(([a,e])=>(0,d.jsxs)("div",{children:["all"===k&&(0,d.jsx)("div",{className:"sticky top-0 px-4 py-3 bg-muted/80 backdrop-blur-sm text-sm font-semibold text-foreground border-b border-border/50 z-10",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:a}),(0,d.jsx)("span",{className:"text-xs text-muted-foreground bg-background px-2 py-1 rounded-full",children:e.length})]})}),e.map(a=>(0,d.jsxs)("div",{onClick:()=>b(a),className:(0,w.cn)("flex items-center p-4 hover:bg-accent/50 cursor-pointer transition-all duration-200 group",c?.id===a.id&&"bg-primary/10 border-r-4 border-primary shadow-sm"),children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-12 h-12 mr-4",children:a.logo&&!y.has(a.id)?(0,d.jsx)("img",{src:a.logo,alt:a.name,className:"w-full h-full object-cover rounded-lg shadow-sm ring-1 ring-border/20",onError:()=>{z(b=>new Set(b).add(a.id))}}):(0,d.jsx)("div",{className:"w-full h-full bg-muted rounded-lg flex items-center justify-center text-muted-foreground text-lg shadow-sm ring-1 ring-border/20",children:"\uD83D\uDCFA"})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("div",{className:"font-semibold text-foreground truncate group-hover:text-primary transition-colors",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground truncate mt-1",children:a.group})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(0,d.jsx)("button",{onClick:b=>{var c;b.stopPropagation(),c=a.id,x(a=>{let b=new Set(a);return b.has(c)?b.delete(c):b.add(c),localStorage.setItem("iptv-favorites",JSON.stringify(Array.from(b))),b})},className:(0,w.cn)("p-1 rounded-md transition-all duration-200 opacity-0 group-hover:opacity-100",s.has(a.id)?"text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20 opacity-100":"text-muted-foreground hover:text-yellow-500 hover:bg-yellow-50 dark:hover:bg-yellow-900/20"),title:s.has(a.id)?"取消收藏":"收藏频道",children:(0,d.jsx)(u,{className:(0,w.cn)("w-4 h-4",s.has(a.id)&&"fill-current")})}),c?.id===a.id&&(0,d.jsxs)("div",{className:"flex items-center gap-1 text-primary",children:[(0,d.jsx)(v,{className:"w-4 h-4 fill-current"}),(0,d.jsx)("span",{className:"text-xs font-medium",children:"播放中"})]})]})]},a.id))]},a))})})]})}let y=j("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),z=j("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),A=j("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),B=j("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),C=j("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),D=j("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),E=j("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),F=j("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function G({sources:a,onSourceAdd:b,onSourceUpdate:c,onSourceDelete:f,onSourceRefresh:g,loading:h=!1,className:i}){let[j,k]=(0,e.useState)(!1),[l,m]=(0,e.useState)(null),[o,p]=(0,e.useState)({name:"",url:"",type:"M3U"}),[q,r]=(0,e.useState)(!1),[s,t]=(0,e.useState)(new Set),u=()=>{p({name:"",url:"",type:"M3U"}),k(!1),m(null)},v=async a=>{if(a.preventDefault(),o.name.trim()&&o.url.trim()){r(!0);try{l?await c(l.id,{name:o.name.trim(),url:o.url.trim(),type:o.type}):await b({name:o.name.trim(),url:o.url.trim(),type:o.type,lastUpdated:new Date,channelCount:0,status:"active"}),u()}catch(a){console.error("Failed to save source:",a)}finally{r(!1)}}},x=async a=>{if(confirm("确定要删除这个订阅源吗？这将同时删除所有相关频道。"))try{await f(a)}catch(a){console.error("Failed to delete source:",a)}},G=async a=>{t(b=>new Set(b).add(a));try{await g(a)}catch(a){console.error("Failed to refresh source:",a)}finally{t(b=>{let c=new Set(b);return c.delete(a),c})}};return(0,d.jsxs)("div",{className:(0,w.cn)("flex flex-col h-full bg-background",i),children:[(0,d.jsx)("div",{className:"p-4 border-b border-border bg-card/50",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-foreground",children:"订阅源管理"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"管理您的IPTV订阅源，支持M3U和M3U8格式"})]}),(0,d.jsx)("div",{className:"flex items-center gap-3",children:(0,d.jsxs)("button",{onClick:()=>k(!0),disabled:h,className:"flex items-center gap-2 px-6 py-2.5 min-w-[120px] bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm font-medium",children:[(0,d.jsx)(B,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"添加"})]})})]})}),(0,d.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[j&&(0,d.jsxs)("div",{className:"bg-card border border-border rounded-lg p-6 shadow-sm",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-4",children:l?"编辑订阅源":"添加订阅源"}),(0,d.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"名称"}),(0,d.jsx)("input",{type:"text",value:o.name,onChange:a=>p(b=>({...b,name:a.target.value})),placeholder:"请输入订阅源名称",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"URL地址"}),(0,d.jsx)("input",{type:"url",value:o.url,onChange:a=>p(b=>({...b,url:a.target.value})),placeholder:"https://example.com/playlist.m3u",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"类型"}),(0,d.jsxs)("select",{value:o.type,onChange:a=>p(b=>({...b,type:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,d.jsx)("option",{value:"M3U",children:"M3U"}),(0,d.jsx)("option",{value:"M3U8",children:"M3U8"}),(0,d.jsx)("option",{value:"URL",children:"URL"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 pt-4",children:[(0,d.jsx)("button",{type:"submit",disabled:q||!o.name.trim()||!o.url.trim(),className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors",children:q?"保存中...":l?"更新":"添加"}),(0,d.jsx)("button",{type:"button",onClick:u,className:"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"取消"})]})]})]}),(0,d.jsx)("div",{className:"space-y-4",children:h?(0,d.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"}),(0,d.jsx)("span",{className:"text-gray-500",children:"加载中..."})]})}):0===a.length?(0,d.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,d.jsx)(C,{className:"w-12 h-12 mx-auto mb-4 opacity-50"}),(0,d.jsx)("div",{className:"text-lg",children:"暂无订阅源"}),(0,d.jsx)("div",{className:"text-sm",children:"点击上方按钮添加您的第一个订阅源"})]}):a.map(a=>(0,d.jsx)("div",{className:"bg-card border border-border rounded-lg p-4 shadow-sm",children:(0,d.jsxs)("div",{className:"flex items-start justify-between gap-4",children:[(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,d.jsx)("h3",{className:"font-semibold text-foreground text-lg truncate",children:a.name}),(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(a=>{switch(a){case"active":return(0,d.jsx)(y,{className:"w-4 h-4 text-green-500"});case"error":return(0,d.jsx)(z,{className:"w-4 h-4 text-red-500"});case"inactive":return(0,d.jsx)(A,{className:"w-4 h-4 text-yellow-500"});default:return(0,d.jsx)(A,{className:"w-4 h-4 text-gray-500"})}})(a.status),(0,d.jsx)("span",{className:"text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full whitespace-nowrap",children:(a=>{switch(a){case"active":return"正常";case"error":return"错误";case"inactive":return"未激活";default:return"未知"}})(a.status)})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,d.jsx)(D,{className:"w-4 h-4 flex-shrink-0"}),(0,d.jsx)("span",{className:"truncate font-mono text-xs bg-muted px-2 py-1 rounded",children:a.url})]}),(0,d.jsxs)("div",{className:"mt-3 space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-muted-foreground",children:"类型:"}),(0,d.jsx)("span",{className:"font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-md",children:a.type})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-muted-foreground",children:"频道数:"}),(0,d.jsxs)("span",{className:"font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-md",children:[a.channelCount," 个"]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-muted-foreground",children:"更新时间:"}),(0,d.jsx)("span",{className:"font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-md text-xs",children:(0,w.Yq)(a.lastUpdated)})]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,d.jsx)("button",{onClick:()=>G(a.id),disabled:s.has(a.id),className:"p-2 text-muted-foreground hover:text-primary hover:bg-accent rounded-md transition-all duration-200",title:"刷新",children:(0,d.jsx)(n,{className:(0,w.cn)("w-4 h-4",s.has(a.id)&&"animate-spin")})}),(0,d.jsx)("button",{onClick:()=>{p({name:a.name,url:a.url,type:a.type}),m(a),k(!0)},className:"p-2 text-muted-foreground hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-md transition-all duration-200",title:"编辑",children:(0,d.jsx)(E,{className:"w-4 h-4"})}),(0,d.jsx)("button",{onClick:()=>x(a.id),className:"p-2 text-muted-foreground hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200",title:"删除",children:(0,d.jsx)(F,{className:"w-4 h-4"})})]})]})},a.id))})]})]})}let H=j("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]),I=j("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),J=j("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),K=j("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);function L({className:a}){let[b,c]=(0,e.useState)("dark"),[f,g]=(0,e.useState)(!1),h=()=>"light"===b?"浅色模式":"深色模式";return f?(0,d.jsxs)("button",{onClick:()=>{let a=["light","dark"],d=(a.indexOf(b)+1)%a.length;c(a[d])},className:(0,w.cn)("flex items-center space-x-2 p-2 rounded-lg transition-colors","text-gray-600 dark:text-gray-400","hover:bg-gray-100 dark:hover:bg-gray-800","focus:outline-none focus:ring-2 focus:ring-blue-500",a),title:h(),children:[(()=>{switch(b){case"light":return(0,d.jsx)(I,{className:"w-4 h-4"});case"dark":return(0,d.jsx)(J,{className:"w-4 h-4"});default:return(0,d.jsx)(K,{className:"w-4 h-4"})}})(),(0,d.jsx)("span",{className:"text-sm hidden sm:inline",children:h()})]}):(0,d.jsx)("button",{className:(0,w.cn)("p-2 rounded-lg",a),children:(0,d.jsx)(K,{className:"w-4 h-4"})})}let M={theme:"dark",language:"zh-CN",autoplay:!1,volume:.7,quality:"auto",subtitles:!1,fullscreenOnPlay:!1,rememberPosition:!0,startMuted:!0};function N({isOpen:a,onClose:b,className:c,autoRefreshEnabled:f,refreshInterval:g,onAutoRefreshChange:h,onRefreshIntervalChange:i}){let[j,l]=(0,e.useState)(M),[m,n]=(0,e.useState)("general"),p=(a,b)=>{let c={...j,[a]:b};l(c),w.IG.set("userSettings",c)};return a?(0,d.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,d.jsxs)("div",{className:(0,w.cn)("bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",c),children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"设置"}),(0,d.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg transition-colors",children:(0,d.jsx)(k,{className:"w-5 h-5"})})]}),(0,d.jsxs)("div",{className:"flex h-[calc(90vh-80px)]",children:[(0,d.jsx)("div",{className:"w-48 border-r border-gray-200 dark:border-gray-700 p-4",children:(0,d.jsxs)("nav",{className:"space-y-2",children:[(0,d.jsx)("button",{onClick:()=>n("general"),className:(0,w.cn)("w-full text-left px-3 py-2 rounded-lg transition-colors","general"===m?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"),children:"常规设置"}),(0,d.jsx)("button",{onClick:()=>n("player"),className:(0,w.cn)("w-full text-left px-3 py-2 rounded-lg transition-colors","player"===m?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"),children:"播放器设置"}),(0,d.jsx)("button",{onClick:()=>n("about"),className:(0,w.cn)("w-full text-left px-3 py-2 rounded-lg transition-colors","about"===m?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"),children:"关于"})]})}),(0,d.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto",children:["general"===m&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"外观设置"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"主题模式"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"选择应用的外观主题"})]}),(0,d.jsx)(L,{})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"语言"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"选择界面语言"})]}),(0,d.jsxs)("select",{value:j.language,onChange:a=>p("language",a.target.value),className:"px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:[(0,d.jsx)("option",{value:"zh-CN",children:"简体中文"}),(0,d.jsx)("option",{value:"zh-TW",children:"繁體中文"}),(0,d.jsx)("option",{value:"en-US",children:"English"})]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"自动刷新设置"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"启用定时刷新"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"自动定时刷新所有订阅源并更新频道列表"})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:f,onChange:a=>h(a.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"刷新间隔"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"设置自动刷新所有订阅源的时间间隔"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(o,{className:"w-4 h-4 text-gray-400"}),(0,d.jsxs)("select",{value:g,onChange:a=>i(parseInt(a.target.value)),disabled:!f,className:"px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50",children:[(0,d.jsx)("option",{value:1,children:"1分钟"}),(0,d.jsx)("option",{value:5,children:"5分钟"}),(0,d.jsx)("option",{value:10,children:"10分钟"}),(0,d.jsx)("option",{value:15,children:"15分钟"}),(0,d.jsx)("option",{value:30,children:"30分钟"}),(0,d.jsx)("option",{value:60,children:"1小时"})]})]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"数据管理"}),(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"清除数据"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"清除所有本地数据"})]}),(0,d.jsxs)("button",{onClick:()=>{confirm("确定要清除所有数据吗？这将删除所有设置、播放历史和收藏。")&&(w.IG.clear(),l(M),window.location.reload())},className:"flex items-center space-x-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded transition-colors",children:[(0,d.jsx)(F,{className:"w-4 h-4"}),(0,d.jsx)("span",{children:"清除"})]})]})})]})]}),"player"===m&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"播放设置"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"自动播放"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"选择频道后自动开始播放"})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:j.autoplay,onChange:a=>p("autoplay",a.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"默认音量"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"播放器的默认音量大小"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(H,{className:"w-4 h-4 text-gray-400"}),(0,d.jsx)("input",{type:"range",min:"0",max:"1",step:"0.1",value:j.volume,onChange:a=>p("volume",parseFloat(a.target.value)),className:"w-20"}),(0,d.jsxs)("span",{className:"text-sm text-gray-500 w-8",children:[Math.round(100*j.volume),"%"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"默认画质"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"播放器的默认画质设置"})]}),(0,d.jsxs)("select",{value:j.quality,onChange:a=>p("quality",a.target.value),className:"px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:[(0,d.jsx)("option",{value:"auto",children:"自动"}),(0,d.jsx)("option",{value:"high",children:"高清"}),(0,d.jsx)("option",{value:"medium",children:"标清"}),(0,d.jsx)("option",{value:"low",children:"流畅"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"播放时全屏"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"开始播放时自动进入全屏模式"})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:j.fullscreenOnPlay,onChange:a=>p("fullscreenOnPlay",a.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"开始播放时静音"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"播放开始时自动静音"})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:j.startMuted,onChange:a=>p("startMuted",a.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"记住播放位置"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"记住上次播放的位置"})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:j.rememberPosition,onChange:a=>p("rememberPosition",a.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]})]})}),"about"===m&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCFA"}),(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:"IPTV Player"}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"现代化的IPTV网页播放器"}),(0,d.jsx)("div",{className:"inline-flex items-center px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full text-sm",children:"版本 1.0.0"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:"功能特性"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsx)("li",{children:"• 支持HLS (M3U8) 直播流播放"}),(0,d.jsx)("li",{children:"• M3U/M3U8订阅源管理"}),(0,d.jsx)("li",{children:"• 响应式设计，支持移动端"}),(0,d.jsx)("li",{children:"• 深色/浅色主题切换"}),(0,d.jsx)("li",{children:"• 频道搜索和分类"}),(0,d.jsx)("li",{children:"• 播放历史记录"})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:"技术栈"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsx)("li",{children:"• Next.js 15 + React 18"}),(0,d.jsx)("li",{children:"• TypeScript + Tailwind CSS"}),(0,d.jsx)("li",{children:"• ArtPlayer + HLS.js"}),(0,d.jsx)("li",{children:"• SQLite 数据库"}),(0,d.jsx)("li",{children:"• Docker 容器化部署"})]})]}),(0,d.jsx)("div",{className:"text-center text-sm text-gray-500 dark:text-gray-400",children:(0,d.jsx)("p",{children:"\xa9 2024 IPTV Player. All rights reserved."})})]})]})]})]})]})}):null}function O({children:a,fallback:b}){return(0,d.jsx)(f().Suspense,{fallback:(0,d.jsx)("div",{children:"Loading..."}),children:a})}function P(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)([]),[g,h]=(0,e.useState)(),[i,j]=(0,e.useState)("channels"),[t,u]=(0,e.useState)(!1),[v,y]=(0,e.useState)(!0),[z,A]=(0,e.useState)(!0),[B,C]=(0,e.useState)(null),[D,E]=(0,e.useState)(!0),[F,H]=(0,e.useState)(!1),[I,J]=(0,e.useState)(5),[K,M]=(0,e.useState)(null),[P,Q]=(0,e.useState)(!1),[R,S]=(0,e.useState)(""),[T,U]=(0,e.useState)(!1),V=async()=>{try{let a=await fetch("/api/sources"),b=await a.json();if(b.success&&b.data)f(b.data);else throw Error(b.error||"获取订阅源失败")}catch(a){throw console.error("Failed to load sources:",a),a}},W=async()=>{try{let a=await fetch("/api/channels"),c=await a.json();if(c.success&&c.data)b(c.data.channels);else throw Error(c.error||"获取频道列表失败")}catch(a){throw console.error("Failed to load channels:",a),a}},X=async()=>{let a=c.filter(a=>"active"===a.status).map(async a=>{try{let b=await fetch(`/api/sources/${a.id}/refresh`,{method:"POST"}),c=await b.json();if(c.success&&c.data)return c.data;throw Error(c.error||"刷新失败")}catch(b){return console.error(`Failed to refresh source ${a.name}:`,b),a}}),b=await Promise.all(a);f(a=>a.map(a=>b.find(b=>b.id===a.id)||a)),await W()},Y=(0,e.useCallback)(async()=>{if(!P){Q(!0);try{await V(),await X(),M(new Date)}catch(a){console.error("Manual refresh failed:",a),C("刷新失败，请检查网络连接")}finally{Q(!1)}}},[P,c]),Z=()=>{if(R.trim()){if(!(a=>{try{let b=new URL(a);return["http:","https:"].includes(b.protocol)}catch{return!1}})(R))return void C("请输入有效的URL地址");h({id:"direct-url",name:"直播链接",url:R.trim(),logo:"",group:"直播链接",sourceId:"direct",createdAt:new Date,updatedAt:new Date}),U(!0),C(null),window.innerWidth<768&&y(!1)}},$=async a=>{try{let b=await fetch("/api/sources",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),c=await b.json();if(c.success&&c.data)f(a=>[...a,c.data]),await W();else throw Error(c.error||"添加订阅源失败")}catch(a){throw console.error("Failed to add source:",a),Error((0,w.H4)(a))}},_=async(a,b)=>{try{let c=await fetch(`/api/sources/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)}),d=await c.json();if(d.success&&d.data)f(b=>b.map(b=>b.id===a?d.data:b)),await W();else throw Error(d.error||"更新订阅源失败")}catch(a){throw console.error("Failed to update source:",a),Error((0,w.H4)(a))}},aa=async a=>{try{let b=await fetch(`/api/sources/${a}`,{method:"DELETE"}),c=await b.json();if(c.success)f(b=>b.filter(b=>b.id!==a)),await W(),g&&g.sourceId===a&&h(void 0);else throw Error(c.error||"删除订阅源失败")}catch(a){throw console.error("Failed to delete source:",a),Error((0,w.H4)(a))}},ab=async a=>{try{let b=await fetch(`/api/sources/${a}/refresh`,{method:"POST"}),c=await b.json();if(c.success&&c.data)f(b=>b.map(b=>b.id===a?c.data:b)),await W(),M(new Date);else throw Error(c.error||"刷新订阅源失败")}catch(a){throw console.error("Failed to refresh source:",a),Error((0,w.H4)(a))}};return z?(0,d.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center",id:"main-loading-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4",id:"main-loading-spinner"}),(0,d.jsx)("div",{className:"text-lg font-medium text-foreground",children:"加载中..."}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"正在初始化IPTV播放器"})]})}):(0,d.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,d.jsxs)("header",{className:"bg-card border-b border-border px-4 py-3 flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("button",{onClick:()=>y(!v),className:"p-2 hover:bg-accent rounded-lg transition-colors lg:hidden",children:v?(0,d.jsx)(k,{className:"w-5 h-5"}):(0,d.jsx)(l,{className:"w-5 h-5"})}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(m,{className:"w-6 h-6 text-primary"}),(0,d.jsx)("h1",{className:"text-xl font-bold",children:"IPTV Player"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("button",{onClick:Y,disabled:P,className:"p-2 hover:bg-accent rounded-lg transition-colors disabled:opacity-50",title:"刷新所有订阅源并更新频道列表",children:(0,d.jsx)(n,{className:`w-4 h-4 ${P?"animate-spin":""}`})}),K&&(0,d.jsxs)("div",{className:"hidden sm:flex items-center space-x-1 text-xs text-muted-foreground",children:[(0,d.jsx)(o,{className:"w-3 h-3"}),(0,d.jsx)("span",{title:"上次刷新所有订阅源的时间",children:K.toLocaleTimeString()})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[D?(0,d.jsx)(p,{className:"w-4 h-4 text-green-500"}):(0,d.jsx)(q,{className:"w-4 h-4 text-red-500"}),(0,d.jsx)("span",{className:"text-xs text-muted-foreground hidden sm:inline",children:D?"在线":"离线"})]}),(0,d.jsx)(L,{}),(0,d.jsx)("button",{onClick:()=>u(!0),className:"p-2 hover:bg-accent rounded-lg transition-colors",children:(0,d.jsx)(r,{className:"w-5 h-5"})})]})]}),(0,d.jsxs)("div",{className:"flex h-[calc(100vh-73px)]",children:[(0,d.jsx)("aside",{className:(0,w.cn)("bg-card border-r border-border transition-all duration-300 flex flex-col",v?"w-96":"w-0","lg:w-96 lg:block",!v&&"lg:w-0 lg:hidden"),children:v&&(0,d.jsxs)("div",{className:"flex flex-col h-full",children:[(0,d.jsxs)("div",{className:"flex border-b border-border",children:[(0,d.jsx)("button",{onClick:()=>j("channels"),className:(0,w.cn)("flex-1 px-4 py-3 text-sm font-medium transition-colors","channels"===i?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"),children:"频道列表"}),(0,d.jsx)("button",{onClick:()=>j("sources"),className:(0,w.cn)("flex-1 px-4 py-3 text-sm font-medium transition-colors","sources"===i?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"),children:"订阅源"})]}),(0,d.jsx)("div",{className:"flex-1 overflow-hidden",children:"channels"===i?(0,d.jsx)(x,{channels:a,onChannelSelect:a=>{h(a),U(!1),window.innerWidth<768&&y(!1)},selectedChannel:g,loading:!1,error:B||void 0}):(0,d.jsx)(G,{sources:c,onSourceAdd:$,onSourceUpdate:_,onSourceDelete:aa,onSourceRefresh:ab,loading:!1})})]})}),(0,d.jsxs)("main",{className:"flex-1 flex flex-col",children:[(0,d.jsx)("div",{className:"p-4 border-b border-border bg-card/50",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(m,{className:"w-4 h-4 text-primary"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"直播链接播放"}),T&&(0,d.jsx)("span",{className:"text-xs bg-primary/10 text-primary px-2 py-1 rounded",children:"当前模式"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("input",{type:"url",value:R,onChange:a=>S(a.target.value),placeholder:"输入直播链接 (http:// 或 https://)",className:"flex-1 px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",onKeyDown:a=>{"Enter"===a.key&&Z()}}),(0,d.jsx)("button",{onClick:Z,disabled:!R.trim(),className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"播放"}),T&&(0,d.jsx)("button",{onClick:()=>{S(""),U(!1),h(void 0)},className:"px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/90 transition-colors",children:"清除"})]})]})}),(0,d.jsx)("div",{className:"flex-1 p-4",children:(0,d.jsx)(O,{children:(0,d.jsx)(s.A,{channel:g,onError:a=>{console.error("Player error:",a),C(`播放错误: ${a.message}`)},className:"w-full h-full"})})}),B&&(0,d.jsxs)("div",{className:"mx-4 mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg",children:[(0,d.jsx)("div",{className:"text-destructive text-sm",children:B}),(0,d.jsx)("button",{onClick:()=>C(null),className:"text-xs text-destructive/70 hover:text-destructive mt-1",children:"关闭"})]})]})]}),(0,d.jsx)(N,{isOpen:t,onClose:()=>u(!1),autoRefreshEnabled:F,refreshInterval:I,onAutoRefreshChange:H,onRefreshIntervalChange:J}),v&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>y(!1)})]})}},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3306:(a,b,c)=>{Promise.resolve().then(c.bind(c,1204))},3873:a=>{"use strict";a.exports=require("path")},4790:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"D:\\Project\\IPTV\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"D:\\Project\\IPTV\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"]}],H=["D:\\Project\\IPTV\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},6354:(a,b,c)=>{Promise.resolve().then(c.bind(c,2469))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,385,69,65],()=>b(b.s=4790));module.exports=c})();