import { NextRequest, NextResponse } from 'next/server'
import { DatabaseOperations } from '@/lib/database'
import { ApiResponse } from '@/types'

interface RouteParams {
  params: Promise<{ id: string }>
}

// PUT /api/sources/[id]/toggle - 切换订阅源启用状态
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params
    const body = await request.json()
    const { enabled } = body

    // 检查源是否存在
    const existingSource = await DatabaseOperations.getSource(resolvedParams.id)
    if (!existingSource) {
      const response: ApiResponse = {
        success: false,
        error: 'Source not found',
        message: `Source with id ${resolvedParams.id} not found`
      }
      return NextResponse.json(response, { status: 404 })
    }

    if (enabled) {
      // 启用此订阅源（会自动禁用其他所有订阅源）
      await DatabaseOperations.enableSource(resolvedParams.id)
    } else {
      // 禁用此订阅源
      await DatabaseOperations.disableSource(resolvedParams.id)
    }

    // 获取更新后的源信息
    const updatedSource = await DatabaseOperations.getSource(resolvedParams.id)
    if (!updatedSource) {
      throw new Error('Failed to retrieve updated source')
    }

    // 转换为API格式
    const apiSource = {
      id: updatedSource.id,
      name: updatedSource.name,
      url: updatedSource.url,
      type: updatedSource.type as 'M3U' | 'M3U8' | 'URL',
      lastUpdated: new Date(updatedSource.last_updated),
      channelCount: updatedSource.channel_count,
      status: updatedSource.status as 'active' | 'inactive' | 'error',
      enabled: Boolean(updatedSource.enabled),
      createdAt: new Date(updatedSource.created_at),
      updatedAt: new Date(updatedSource.updated_at)
    }

    const response: ApiResponse = {
      success: true,
      data: apiSource,
      message: enabled ? 'Source enabled successfully' : 'Source disabled successfully'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Failed to toggle source:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Failed to toggle source'
    }
    return NextResponse.json(response, { status: 500 })
  }
}
