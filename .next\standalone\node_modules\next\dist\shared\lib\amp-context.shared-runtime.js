"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AmpStateContext", {
    enumerable: true,
    get: function() {
        return AmpStateContext;
    }
});
const _interop_require_default = require("@swc/helpers/_/_interop_require_default");
const _react = /*#__PURE__*/ _interop_require_default._(require("react"));
const AmpStateContext = _react.default.createContext({});
if (process.env.NODE_ENV !== 'production') {
    AmpStateContext.displayName = 'AmpStateContext';
}

//# sourceMappingURL=amp-context.shared-runtime.js.map