import { NextRequest, NextResponse } from 'next/server'
import { DatabaseOperations } from '@/lib/database'
import { M3UParser } from '@/lib/m3u-parser'
import { ApiResponse, Source } from '@/types'

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

// POST /api/sources/[id]/refresh - 刷新订阅源
export async function POST(_request: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params
    // 检查源是否存在
    const existingSource = await DatabaseOperations.getSource(resolvedParams.id)
    if (!existingSource) {
      const response: ApiResponse = {
        success: false,
        error: 'Source not found',
        message: `Source with id ${resolvedParams.id} not found`
      }
      return NextResponse.json(response, { status: 404 })
    }

    // 更新源状态为正在刷新
    await DatabaseOperations.updateSource(resolvedParams.id, {
      status: 'inactive',
      last_updated: new Date().toISOString()
    })

    try {
      // 重新解析和添加频道
      await parseAndAddChannels(resolvedParams.id, existingSource.url, existingSource.type)

      // 获取更新后的源
      const refreshedSource = await DatabaseOperations.getSource(resolvedParams.id)
      if (!refreshedSource) {
        throw new Error('Failed to retrieve refreshed source')
      }

      const apiSource: Source = {
        id: refreshedSource.id,
        name: refreshedSource.name,
        url: refreshedSource.url,
        type: refreshedSource.type as 'M3U' | 'M3U8' | 'URL',
        lastUpdated: new Date(refreshedSource.last_updated),
        channelCount: refreshedSource.channel_count,
        status: refreshedSource.status as 'active' | 'inactive' | 'error',
        enabled: Boolean(refreshedSource.enabled),
        createdAt: new Date(refreshedSource.created_at),
        updatedAt: new Date(refreshedSource.updated_at)
      }

      const response: ApiResponse<Source> = {
        success: true,
        data: apiSource,
        message: `Source refreshed successfully. Found ${refreshedSource.channel_count} channels.`
      }

      return NextResponse.json(response)
    } catch (parseError) {
      console.error('Failed to refresh source:', parseError)
      
      // 更新源状态为错误
      await DatabaseOperations.updateSource(resolvedParams.id, {
        status: 'error',
        last_updated: new Date().toISOString()
      })

      const response: ApiResponse = {
        success: false,
        error: 'Failed to refresh source',
        message: parseError instanceof Error ? parseError.message : 'Unknown error occurred while parsing'
      }
      return NextResponse.json(response, { status: 400 })
    }
  } catch (error) {
    console.error('Failed to refresh source:', error)
    const response: ApiResponse = {
      success: false,
      error: 'Failed to refresh source',
      message: error instanceof Error ? error.message : 'Unknown error'
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// 解析M3U内容并添加频道
async function parseAndAddChannels(sourceId: string, url: string, type: string): Promise<void> {
  if (type !== 'M3U' && type !== 'M3U8') {
    // 对于非M3U类型，只更新状态
    await DatabaseOperations.updateSource(sourceId, {
      status: 'active',
      last_updated: new Date().toISOString()
    })
    return
  }

  // 获取M3U内容
  const content = await M3UParser.fetchFromUrl(url)
  
  // 验证内容
  const validation = M3UParser.validate(content)
  if (!validation.valid) {
    throw new Error(`Invalid M3U content: ${validation.errors.join(', ')}`)
  }

  // 解析内容
  const parsed = M3UParser.parse(content)
  
  if (parsed.entries.length === 0) {
    throw new Error('No valid channels found in the M3U file')
  }

  // 删除该源的现有频道
  await DatabaseOperations.deleteChannelsBySource(sourceId)
  
  // 添加新频道
  let addedCount = 0
  const errors: string[] = []
  
  for (const entry of parsed.entries) {
    try {
      // 验证频道数据
      if (!entry.title || !entry.url) {
        errors.push(`Skipped invalid channel: ${entry.title || 'Unknown'}`)
        continue
      }

      // 验证URL格式
      try {
        new URL(entry.url)
      } catch {
        errors.push(`Skipped channel with invalid URL: ${entry.title}`)
        continue
      }

      await DatabaseOperations.createChannel({
        name: entry.title.trim(),
        url: entry.url.trim(),
        group_name: (entry.group || 'Default').trim(),
        logo_url: entry.logo?.trim() || undefined,
        source_id: sourceId
      })
      addedCount++
    } catch (error) {
      const errorMsg = `Failed to add channel ${entry.title}: ${error instanceof Error ? error.message : 'Unknown error'}`
      errors.push(errorMsg)
      console.warn(errorMsg)
    }
  }

  // 记录错误但不阻止成功
  if (errors.length > 0) {
    console.warn(`Refresh completed with ${errors.length} errors:`, errors)
  }

  // 更新源的频道数量和状态
  await DatabaseOperations.updateSource(sourceId, {
    channel_count: addedCount,
    status: addedCount > 0 ? 'active' : 'error',
    last_updated: new Date().toISOString()
  })

  if (addedCount === 0) {
    throw new Error('No channels were successfully added. Please check the M3U file format.')
  }
}
