import { DatabaseChannel, DatabaseSource, DatabaseSettings } from '@/types'

// 动态导入SQLite3和相关模块
let sqlite3: any = null
let path: any = null
let fs: any = null

// 初始化数据库模块
async function initDatabaseModules() {
  if (typeof window !== 'undefined') {
    throw new Error('Database operations are only available on the server side')
  }

  if (!sqlite3) {
    try {
      sqlite3 = require('sqlite3').verbose()
      path = require('path')
      fs = require('fs')
    } catch (error) {
      console.error('Failed to load database modules:', error)
      throw new Error('Database modules not available')
    }
  }
}

// 获取数据库路径的函数
function getDbPath(): string {
  if (process.env.DATABASE_URL) {
    return process.env.DATABASE_URL
  }

  // 如果path模块已加载，使用绝对路径
  if (path) {
    return path.resolve(process.cwd(), 'database', 'iptv.db')
  }

  // 回退到相对路径
  return './database/iptv.db'
}

// 确保数据库目录存在
async function ensureDatabaseDirectory() {
  await initDatabaseModules()

  if (path && fs) {
    const dbPath = getDbPath()
    const dbDir = path.dirname(dbPath)
    if (!fs.existsSync(dbDir)) {
      console.log('创建数据库目录:', dbDir)
      fs.mkdirSync(dbDir, { recursive: true })
    }
  }
}

// 数据库连接池
class DatabasePool {
  private static instance: DatabasePool
  private db: any | null = null

  private constructor() {}

  public static getInstance(): DatabasePool {
    if (!DatabasePool.instance) {
      DatabasePool.instance = new DatabasePool()
    }
    return DatabasePool.instance
  }

  public async getConnection(): Promise<any> {
    await initDatabaseModules()
    await ensureDatabaseDirectory()

    if (!this.db) {
      // 获取实际的数据库路径
      const actualDbPath = getDbPath()
      console.log('尝试连接数据库:', actualDbPath)
      console.log('当前工作目录:', process.cwd())

      // 检查文件是否存在
      if (fs && !fs.existsSync(actualDbPath)) {
        console.log('数据库文件不存在，将创建新文件')
      } else if (fs) {
        console.log('数据库文件已存在')
      }

      this.db = new sqlite3.Database(actualDbPath, (err: any) => {
        if (err) {
          console.error('数据库连接失败:', err.message)
          console.error('数据库路径:', actualDbPath)
          console.error('当前工作目录:', process.cwd())
          console.error('错误代码:', err.code)
          console.error('错误编号:', err.errno)
          throw err
        }
        console.log('数据库连接成功:', actualDbPath)
      })
      
      // 启用外键约束
      this.db.run('PRAGMA foreign_keys = ON')
      
      // 初始化数据库表
      await this.initializeTables()
    }
    return this.db
  }

  private async initializeTables(): Promise<void> {
    if (!this.db) return

    const tables = [
      // 订阅源表
      `CREATE TABLE IF NOT EXISTS sources (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        type TEXT CHECK(type IN ('M3U', 'M3U8', 'URL')) NOT NULL,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        channel_count INTEGER DEFAULT 0,
        status TEXT CHECK(status IN ('active', 'inactive', 'error')) DEFAULT 'active',
        enabled BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 频道表
      `CREATE TABLE IF NOT EXISTS channels (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        group_name TEXT DEFAULT 'Default',
        logo_url TEXT,
        source_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (source_id) REFERENCES sources (id) ON DELETE CASCADE
      )`,
      
      // 用户设置表
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 播放历史表
      `CREATE TABLE IF NOT EXISTS play_history (
        id TEXT PRIMARY KEY,
        channel_id TEXT,
        channel_name TEXT NOT NULL,
        played_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        duration INTEGER DEFAULT 0,
        position INTEGER DEFAULT 0,
        FOREIGN KEY (channel_id) REFERENCES channels (id) ON DELETE CASCADE
      )`,
      
      // 收藏表
      `CREATE TABLE IF NOT EXISTS favorites (
        id TEXT PRIMARY KEY,
        channel_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (channel_id) REFERENCES channels (id) ON DELETE CASCADE
      )`,
      
      // 索引
      `CREATE INDEX IF NOT EXISTS idx_channels_source_id ON channels (source_id)`,
      `CREATE INDEX IF NOT EXISTS idx_channels_group_name ON channels (group_name)`,
      `CREATE INDEX IF NOT EXISTS idx_play_history_channel_id ON play_history (channel_id)`,
      `CREATE INDEX IF NOT EXISTS idx_play_history_played_at ON play_history (played_at)`,
      `CREATE INDEX IF NOT EXISTS idx_favorites_channel_id ON favorites (channel_id)`
    ]

    for (const sql of tables) {
      await this.run(sql)
    }
  }

  public async run(sql: string, params: any[] = []): Promise<void> {
    const db = await this.getConnection()
    return new Promise((resolve, reject) => {
      db.run(sql, params, function(err: any) {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }

  public async get<T = any>(sql: string, params: any[] = []): Promise<T | undefined> {
    const db = await this.getConnection()
    return new Promise((resolve, reject) => {
      db.get(sql, params, (err: any, row: any) => {
        if (err) {
          reject(err)
        } else {
          resolve(row as T)
        }
      })
    })
  }

  public async all<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    const db = await this.getConnection()
    return new Promise((resolve, reject) => {
      db.all(sql, params, (err: any, rows: any) => {
        if (err) {
          reject(err)
        } else {
          resolve(rows as T[])
        }
      })
    })
  }

  public async close(): Promise<void> {
    if (this.db) {
      return new Promise((resolve, reject) => {
        this.db!.close((err: any) => {
          if (err) {
            reject(err)
          } else {
            this.db = null
            resolve()
          }
        })
      })
    }
  }

  // 测试数据库连接
  public async testConnection(): Promise<boolean> {
    try {
      const db = await this.getConnection()
      return new Promise((resolve) => {
        db.get('SELECT 1', (err: any) => {
          resolve(!err)
        })
      })
    } catch (error) {
      console.error('Database connection test failed:', error)
      return false
    }
  }
}

// 导出数据库实例
export const db = DatabasePool.getInstance()

// 数据库操作函数
export class DatabaseOperations {
  // 源操作
  static async createSource(source: Omit<DatabaseSource, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    const id = `src_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    await db.run(
      `INSERT INTO sources (id, name, url, type, last_updated, channel_count, status, enabled)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [id, source.name, source.url, source.type, source.last_updated, source.channel_count, source.status, source.enabled]
    )
    return id
  }

  static async getSources(): Promise<DatabaseSource[]> {
    return await db.all<DatabaseSource>('SELECT * FROM sources ORDER BY created_at DESC')
  }

  static async getSource(id: string): Promise<DatabaseSource | undefined> {
    return await db.get<DatabaseSource>('SELECT * FROM sources WHERE id = ?', [id])
  }

  static async updateSource(id: string, updates: Partial<DatabaseSource>): Promise<void> {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ')
    const values = Object.values(updates)
    await db.run(
      `UPDATE sources SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    )
  }

  static async deleteSource(id: string): Promise<void> {
    await db.run('DELETE FROM sources WHERE id = ?', [id])
  }

  // 启用订阅源（同时禁用其他所有订阅源）
  static async enableSource(id: string): Promise<void> {
    // 先禁用所有订阅源
    await db.run('UPDATE sources SET enabled = 0, updated_at = CURRENT_TIMESTAMP')
    // 然后启用指定的订阅源
    await db.run('UPDATE sources SET enabled = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [id])
  }

  // 禁用订阅源
  static async disableSource(id: string): Promise<void> {
    await db.run('UPDATE sources SET enabled = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [id])
  }

  // 获取当前启用的订阅源
  static async getEnabledSource(): Promise<DatabaseSource | undefined> {
    return await db.get<DatabaseSource>('SELECT * FROM sources WHERE enabled = 1 LIMIT 1')
  }

  // 频道操作
  static async createChannel(channel: Omit<DatabaseChannel, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    const id = `ch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    await db.run(
      `INSERT INTO channels (id, name, url, group_name, logo_url, source_id) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [id, channel.name, channel.url, channel.group_name, channel.logo_url, channel.source_id]
    )
    return id
  }

  static async getChannels(sourceId?: string, groupName?: string): Promise<DatabaseChannel[]> {
    let sql = 'SELECT * FROM channels'
    const params: any[] = []
    
    if (sourceId || groupName) {
      sql += ' WHERE'
      const conditions = []
      
      if (sourceId) {
        conditions.push(' source_id = ?')
        params.push(sourceId)
      }
      
      if (groupName) {
        conditions.push(' group_name = ?')
        params.push(groupName)
      }
      
      sql += conditions.join(' AND')
    }
    
    sql += ' ORDER BY group_name, name'
    return await db.all<DatabaseChannel>(sql, params)
  }

  static async getChannel(id: string): Promise<DatabaseChannel | undefined> {
    return await db.get<DatabaseChannel>('SELECT * FROM channels WHERE id = ?', [id])
  }

  static async updateChannel(id: string, updates: Partial<DatabaseChannel>): Promise<void> {
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ')
    const values = Object.values(updates)
    await db.run(
      `UPDATE channels SET ${fields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    )
  }

  static async deleteChannel(id: string): Promise<void> {
    await db.run('DELETE FROM channels WHERE id = ?', [id])
  }

  static async deleteChannelsBySource(sourceId: string): Promise<void> {
    await db.run('DELETE FROM channels WHERE source_id = ?', [sourceId])
  }

  // 设置操作
  static async getSetting(key: string): Promise<string | undefined> {
    const result = await db.get<DatabaseSettings>('SELECT value FROM settings WHERE key = ?', [key])
    return result?.value
  }

  static async setSetting(key: string, value: string): Promise<void> {
    await db.run(
      `INSERT OR REPLACE INTO settings (key, value, updated_at) 
       VALUES (?, ?, CURRENT_TIMESTAMP)`,
      [key, value]
    )
  }

  static async getSettings(): Promise<Record<string, string>> {
    const rows = await db.all<DatabaseSettings>('SELECT key, value FROM settings')
    return rows.reduce((acc, row) => {
      acc[row.key] = row.value
      return acc
    }, {} as Record<string, string>)
  }

  // 测试数据库连接
  static async testConnection(): Promise<boolean> {
    return await db.testConnection()
  }
}
